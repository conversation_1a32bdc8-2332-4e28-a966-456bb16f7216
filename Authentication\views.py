from django.shortcuts import render, redirect
from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.views import View
from django.utils.decorators import method_decorator
from django.http import JsonResponse


class AdminLoginView(View):
    """Admin login view"""

    def get(self, request):
        if request.user.is_authenticated and request.user.is_staff:
            return redirect('/admin/')
        return render(request, 'admin_login.html')

    def post(self, request):
        username = request.POST.get('username')
        password = request.POST.get('password')

        if not username or not password:
            messages.error(request, 'Please provide both username and password')
            return render(request, 'admin_login.html')

        user = authenticate(request, username=username, password=password)

        if user is not None and user.is_staff:
            login(request, user)
            return redirect('/admin/')
        else:
            messages.error(request, 'Invalid credentials or insufficient permissions')
            return render(request, 'admin_login.html')


class AdminLogoutView(View):
    """Admin logout view"""

    def get(self, request):
        logout(request)
        messages.success(request, 'You have been logged out successfully')
        return redirect('/admin/login/')


@method_decorator(login_required, name='dispatch')
class AdminDashboardView(View):
    """Admin dashboard with statistics"""

    def get(self, request):
        if not request.user.is_staff:
            return redirect('/admin/login/')

        from Project.models import Event, Photo, FaceEncoding, UserSearch, PhotoDownload

        # Get statistics
        stats = {
            'total_events': Event.objects.count(),
            'total_photos': Photo.objects.count(),
            'total_faces': FaceEncoding.objects.count(),
            'total_searches': UserSearch.objects.count(),
            'total_downloads': PhotoDownload.objects.count(),
            'recent_events': Event.objects.order_by('-created_at')[:5],
            'recent_searches': UserSearch.objects.order_by('-search_time')[:10],
            'recent_downloads': PhotoDownload.objects.select_related('photo', 'photo__event').order_by('-download_time')[:10],
        }

        return render(request, 'admin_dashboard.html', {'stats': stats})