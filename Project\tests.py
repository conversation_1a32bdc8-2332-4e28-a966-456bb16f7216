from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.core.files.uploadedfile import SimpleUploadedFile
from django.urls import reverse
from .models import Event, Photo, FaceEncoding
import json
from datetime import date


class FaceRecognitionTestCase(TestCase):
    def setUp(self):
        """Set up test data"""
        self.client = Client()

        # Create admin user
        self.admin_user = User.objects.create_user(
            username='admin',
            password='testpass123',
            is_staff=True,
            is_superuser=True
        )

        # Create test event
        self.event = Event.objects.create(
            name='Test Event',
            date=date.today(),
            location='Test Location'
        )

    def test_event_creation(self):
        """Test event model creation"""
        self.assertEqual(self.event.name, 'Test Event')
        self.assertEqual(self.event.location, 'Test Location')
        self.assertEqual(str(self.event), f'Test Event ({date.today()})')

    def test_bulk_upload_view_get(self):
        """Test bulk upload view GET request"""
        self.client.login(username='admin', password='testpass123')
        response = self.client.get('/admin/bulk-upload/')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Test Event')

    def test_bulk_upload_view_post_no_files(self):
        """Test bulk upload with no files"""
        self.client.login(username='admin', password='testpass123')
        response = self.client.post('/admin/bulk-upload/', {
            'event': self.event.id
        })
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.content)
        self.assertFalse(data['success'])

    def test_user_search_view_get(self):
        """Test user search view GET request"""
        response = self.client.get('/find-your-photos/')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Find Your Photos')

    def test_user_search_view_post_no_file(self):
        """Test user search with no file"""
        response = self.client.post('/find-your-photos/')
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.content)
        self.assertIn('error', data)

    def test_photo_model(self):
        """Test photo model"""
        # Create a simple test image file
        image_content = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90wS\xde\x00\x00\x00\tpHYs\x00\x00\x0b\x13\x00\x00\x0b\x13\x01\x00\x9a\x9c\x18\x00\x00\x00\nIDATx\x9cc\xf8\x00\x00\x00\x01\x00\x01\x00\x00\x00\x00IEND\xaeB`\x82'

        uploaded_file = SimpleUploadedFile(
            name='test_image.png',
            content=image_content,
            content_type='image/png'
        )

        photo = Photo.objects.create(
            event=self.event,
            image=uploaded_file
        )

        self.assertEqual(photo.event, self.event)
        self.assertEqual(photo.face_count, 0)
        self.assertFalse(photo.processed)

    def test_face_encoding_model(self):
        """Test face encoding model"""
        # Create a photo first
        uploaded_file = SimpleUploadedFile(
            name='test_image.png',
            content=b'fake image content',
            content_type='image/png'
        )

        photo = Photo.objects.create(
            event=self.event,
            image=uploaded_file
        )

        # Create face encoding
        face_encoding = FaceEncoding.objects.create(
            photo=photo,
            encoding=b'fake encoding data',
            bounding_box='[10, 20, 30, 40]',
            confidence=0.85
        )

        self.assertEqual(face_encoding.photo, photo)
        self.assertEqual(face_encoding.confidence, 0.85)
        self.assertEqual(str(face_encoding), f'Face in Photo {photo.id}')

    def test_file_validation(self):
        """Test file validation functionality"""
        from .views import BulkUploadView

        view = BulkUploadView()

        # Test valid image
        valid_file = SimpleUploadedFile(
            name='test.jpg',
            content=b'fake image content',
            content_type='image/jpeg'
        )
        valid_file.size = 1024  # 1KB

        result = view.validate_image_file(valid_file)
        self.assertTrue(result['valid'])

        # Test invalid file type
        invalid_file = SimpleUploadedFile(
            name='test.txt',
            content=b'text content',
            content_type='text/plain'
        )

        result = view.validate_image_file(invalid_file)
        self.assertFalse(result['valid'])
        self.assertIn('Not an image file', result['reason'])

    def test_admin_authentication_required(self):
        """Test that admin views require authentication"""
        response = self.client.get('/admin/bulk-upload/')
        # Should redirect to login
        self.assertEqual(response.status_code, 302)

    def test_event_statistics(self):
        """Test event statistics properties"""
        # Create some photos
        for i in range(3):
            uploaded_file = SimpleUploadedFile(
                name=f'test_image_{i}.png',
                content=b'fake image content',
                content_type='image/png'
            )
            Photo.objects.create(event=self.event, image=uploaded_file)

        self.assertEqual(self.event.total_photos, 3)
        self.assertEqual(self.event.total_faces, 0)  # No face encodings created yet
