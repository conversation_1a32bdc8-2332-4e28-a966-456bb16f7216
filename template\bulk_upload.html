<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bulk Upload Photos - Admin Panel</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .upload-section {
            padding: 40px;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }

        .form-group select {
            width: 100%;
            padding: 15px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 1em;
            background: white;
            transition: border-color 0.3s ease;
        }

        .form-group select:focus {
            outline: none;
            border-color: #667eea;
        }

        .upload-area {
            border: 3px dashed #667eea;
            border-radius: 15px;
            padding: 40px;
            text-align: center;
            background: #f8f9ff;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .upload-area:hover {
            border-color: #764ba2;
            background: #f0f2ff;
        }

        .upload-area.dragover {
            border-color: #764ba2;
            background: #e8ebff;
            transform: scale(1.02);
        }

        .file-input {
            display: none;
        }

        .upload-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .upload-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .submit-btn {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            color: white;
            border: none;
            padding: 15px 40px;
            border-radius: 25px;
            font-size: 1.2em;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin-top: 20px;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(17, 153, 142, 0.3);
        }

        .submit-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .file-list {
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
        }

        .file-item {
            display: flex;
            align-items: center;
            padding: 10px;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            margin-bottom: 10px;
            background: #f8f9fa;
        }

        .file-preview {
            width: 50px;
            height: 50px;
            object-fit: cover;
            border-radius: 5px;
            margin-right: 15px;
        }

        .file-info {
            flex: 1;
        }

        .file-name {
            font-weight: 600;
            color: #333;
        }

        .file-size {
            color: #666;
            font-size: 0.9em;
        }

        .remove-btn {
            background: #ff6b6b;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.8em;
        }

        .remove-btn:hover {
            background: #ff5252;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e1e5e9;
            border-radius: 10px;
            overflow: hidden;
            margin: 20px 0;
            display: none;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            width: 0%;
            transition: width 0.3s ease;
        }

        .status-message {
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: center;
            display: none;
        }

        .status-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .back-link {
            display: inline-block;
            margin-top: 20px;
            color: #667eea;
            text-decoration: none;
            font-weight: 600;
        }

        .back-link:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📁 Bulk Upload Photos</h1>
            <p>Upload multiple photos for face recognition processing</p>
        </div>

        <div class="upload-section">
            <form id="uploadForm" method="post" enctype="multipart/form-data">
                {% csrf_token %}

                <div class="form-group">
                    <label for="event">📅 Select Event:</label>
                    <select name="event" id="event" required>
                        <option value="">Choose an event...</option>
                        {% for event in events %}
                            <option value="{{ event.id }}">{{ event.name }} ({{ event.date }})</option>
                        {% endfor %}
                    </select>
                </div>

                <div class="form-group">
                    <label>📸 Upload Photos:</label>
                    <div class="upload-area" id="uploadArea">
                        <h3>Drag and drop photos here</h3>
                        <p>or click to browse files</p>
                        <input type="file" name="images" class="file-input" id="fileInput" multiple accept="image/*" required>
                        <button type="button" class="upload-btn" onclick="document.getElementById('fileInput').click()">
                            Choose Photos
                        </button>
                    </div>

                    <div class="file-list" id="fileList"></div>
                </div>

                <div class="progress-bar" id="progressBar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>

                <div class="status-message" id="statusMessage"></div>

                <button type="submit" class="submit-btn" id="submitBtn" disabled>
                    🚀 Upload and Process Photos
                </button>
            </form>

            <a href="/admin/" class="back-link">← Back to Admin Panel</a>
        </div>
    </div>

    <script>
        const fileInput = document.getElementById('fileInput');
        const uploadArea = document.getElementById('uploadArea');
        const fileList = document.getElementById('fileList');
        const submitBtn = document.getElementById('submitBtn');
        const progressBar = document.getElementById('progressBar');
        const progressFill = document.getElementById('progressFill');
        const statusMessage = document.getElementById('statusMessage');

        let selectedFiles = [];

        // Drag and drop functionality
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = Array.from(e.dataTransfer.files);
            addFiles(files);
        });

        fileInput.addEventListener('change', (e) => {
            const files = Array.from(e.target.files);
            addFiles(files);
        });

        function addFiles(files) {
            files.forEach(file => {
                if (file.type.startsWith('image/')) {
                    selectedFiles.push(file);
                }
            });
            updateFileList();
            updateSubmitButton();
        }

        function removeFile(index) {
            selectedFiles.splice(index, 1);
            updateFileList();
            updateSubmitButton();
        }

        function updateFileList() {
            fileList.innerHTML = '';
            selectedFiles.forEach((file, index) => {
                const fileItem = document.createElement('div');
                fileItem.className = 'file-item';

                const reader = new FileReader();
                reader.onload = function(e) {
                    fileItem.innerHTML = `
                        <img src="${e.target.result}" class="file-preview" alt="Preview">
                        <div class="file-info">
                            <div class="file-name">${file.name}</div>
                            <div class="file-size">${formatFileSize(file.size)}</div>
                        </div>
                        <button type="button" class="remove-btn" onclick="removeFile(${index})">Remove</button>
                    `;
                };
                reader.readAsDataURL(file);

                fileList.appendChild(fileItem);
            });
        }

        function updateSubmitButton() {
            const eventSelected = document.getElementById('event').value;
            submitBtn.disabled = !(selectedFiles.length > 0 && eventSelected);
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        document.getElementById('event').addEventListener('change', updateSubmitButton);

        // Form submission
        document.getElementById('uploadForm').onsubmit = async function(e) {
            e.preventDefault();

            const formData = new FormData();
            formData.append('event', document.getElementById('event').value);

            selectedFiles.forEach(file => {
                formData.append('images', file);
            });

            // Show progress
            progressBar.style.display = 'block';
            submitBtn.disabled = true;
            statusMessage.style.display = 'none';

            try {
                const xhr = new XMLHttpRequest();

                xhr.upload.addEventListener('progress', (e) => {
                    if (e.lengthComputable) {
                        const percentComplete = (e.loaded / e.total) * 100;
                        progressFill.style.width = percentComplete + '%';
                    }
                });

                xhr.onload = function() {
                    progressBar.style.display = 'none';

                    if (xhr.status === 200) {
                        const response = JSON.parse(xhr.responseText);
                        if (response.success) {
                            statusMessage.className = 'status-message status-success';
                            statusMessage.textContent = response.message;
                            statusMessage.style.display = 'block';

                            // Reset form
                            selectedFiles = [];
                            updateFileList();
                            document.getElementById('event').value = '';
                            updateSubmitButton();
                        } else {
                            throw new Error(response.error || 'Upload failed');
                        }
                    } else {
                        throw new Error('Upload failed');
                    }
                };

                xhr.onerror = function() {
                    progressBar.style.display = 'none';
                    statusMessage.className = 'status-message status-error';
                    statusMessage.textContent = 'Upload failed. Please try again.';
                    statusMessage.style.display = 'block';
                    submitBtn.disabled = false;
                };

                xhr.open('POST', '');
                xhr.send(formData);

            } catch (error) {
                progressBar.style.display = 'none';
                statusMessage.className = 'status-message status-error';
                statusMessage.textContent = 'Upload failed: ' + error.message;
                statusMessage.style.display = 'block';
                submitBtn.disabled = false;
            }
        };
    </script>
</body>
</html>