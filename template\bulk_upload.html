<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FaceScan Pro - Admin Upload</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            --glass-bg: rgba(255, 255, 255, 0.1);
            --glass-border: rgba(255, 255, 255, 0.2);
            --shadow-light: 0 8px 32px rgba(31, 38, 135, 0.37);
            --shadow-heavy: 0 20px 60px rgba(0, 0, 0, 0.15);
        }
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: var(--primary-gradient);
            min-height: 100vh;
            overflow-x: hidden;
            position: relative;
        }

        /* Animated Background */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%);
            z-index: -1;
            animation: backgroundShift 15s ease-in-out infinite;
        }

        @keyframes backgroundShift {
            0%, 100% { transform: scale(1) rotate(0deg); }
            50% { transform: scale(1.1) rotate(3deg); }
        }

        /* Navigation */
        .navbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--glass-border);
            padding: 1rem 2rem;
            z-index: 1000;
        }

        .nav-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 12px;
            color: white;
            font-weight: 800;
            font-size: 1.5rem;
            text-decoration: none;
        }

        .logo-icon {
            width: 40px;
            height: 40px;
            background: var(--secondary-gradient);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            box-shadow: var(--shadow-light);
        }

        .container {
            max-width: 900px;
            margin: 120px auto 40px;
            padding: 0 20px;
        }

        .main-card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 24px;
            padding: 3rem;
            box-shadow: var(--shadow-heavy);
        }

        .header-section {
            text-align: center;
            margin-bottom: 3rem;
            color: white;
        }

        .header-title {
            font-size: clamp(2rem, 4vw, 3rem);
            font-weight: 800;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, #fff 0%, #f0f0f0 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            max-width: 500px;
            margin: 0 auto;
        }

        .upload-section {
            padding: 40px;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }

        .form-group select {
            width: 100%;
            padding: 15px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 1em;
            background: white;
            transition: border-color 0.3s ease;
        }

        .form-group select:focus {
            outline: none;
            border-color: #667eea;
        }

        .upload-area {
            border: 3px dashed #667eea;
            border-radius: 15px;
            padding: 40px;
            text-align: center;
            background: #f8f9ff;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .upload-area:hover {
            border-color: #764ba2;
            background: #f0f2ff;
        }

        .upload-area.dragover {
            border-color: #764ba2;
            background: #e8ebff;
            transform: scale(1.02);
        }

        .file-input {
            display: none;
        }

        .upload-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .upload-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .submit-btn {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            color: white;
            border: none;
            padding: 15px 40px;
            border-radius: 25px;
            font-size: 1.2em;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin-top: 20px;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(17, 153, 142, 0.3);
        }

        .submit-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .file-list {
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
        }

        .file-item {
            display: flex;
            align-items: center;
            padding: 10px;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            margin-bottom: 10px;
            background: #f8f9fa;
        }

        .file-preview {
            width: 50px;
            height: 50px;
            object-fit: cover;
            border-radius: 5px;
            margin-right: 15px;
        }

        .file-info {
            flex: 1;
        }

        .file-name {
            font-weight: 600;
            color: #333;
        }

        .file-size {
            color: #666;
            font-size: 0.9em;
        }

        .remove-btn {
            background: #ff6b6b;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.8em;
        }

        .remove-btn:hover {
            background: #ff5252;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e1e5e9;
            border-radius: 10px;
            overflow: hidden;
            margin: 20px 0;
            display: none;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            width: 0%;
            transition: width 0.3s ease;
        }

        .status-message {
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: center;
            display: none;
        }

        .status-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .back-link {
            display: inline-block;
            margin-top: 20px;
            color: #667eea;
            text-decoration: none;
            font-weight: 600;
        }

        .back-link:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-content">
            <a href="/" class="logo">
                <div class="logo-icon">
                    <i class="fas fa-upload"></i>
                </div>
                <span>FaceScan Pro</span>
            </a>
            <div style="display: flex; gap: 1rem; align-items: center;">
                <a href="/" style="color: white; text-decoration: none; padding: 0.5rem 1rem; border-radius: 8px; transition: all 0.3s ease;">
                    <i class="fas fa-home"></i> Home
                </a>
                <a href="/admin/" style="color: white; text-decoration: none; padding: 0.5rem 1rem; border-radius: 8px; transition: all 0.3s ease;">
                    <i class="fas fa-cog"></i> Admin
                </a>
            </div>
        </div>
    </nav>

    <div class="container">
        <div class="main-card">
            <div class="header-section">
                <h1 class="header-title">
                    <i class="fas fa-cloud-upload-alt" style="color: #4facfe;"></i>
                    Bulk Photo Upload
                </h1>
                <p class="header-subtitle">
                    Upload multiple photos for AI-powered face recognition processing
                </p>
            </div>

            <form id="uploadForm" method="post" enctype="multipart/form-data">
                {% csrf_token %}

                <div class="form-group">
                    <label for="event">
                        <i class="fas fa-calendar-alt"></i>
                        Select Event:
                    </label>
                    <select name="event" id="event" required>
                        <option value="">Choose an event...</option>
                        {% for event in events %}
                            <option value="{{ event.id }}">{{ event.name }} ({{ event.date }})</option>
                        {% endfor %}
                    </select>
                </div>

                <div class="form-group">
                    <label>📸 Upload Photos:</label>
                    <div class="upload-area" id="uploadArea">
                        <h3>Drag and drop photos here</h3>
                        <p>or click to browse files</p>
                        <input type="file" name="images" class="file-input" id="fileInput" multiple accept="image/*" required>
                        <button type="button" class="upload-btn" onclick="document.getElementById('fileInput').click()">
                            Choose Photos
                        </button>
                    </div>

                    <div class="file-list" id="fileList"></div>
                </div>

                <div class="progress-bar" id="progressBar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>

                <div class="status-message" id="statusMessage"></div>

                <button type="submit" class="submit-btn" id="submitBtn" disabled>
                    🚀 Upload and Process Photos
                </button>
            </form>

            <a href="/admin/" class="back-link">← Back to Admin Panel</a>
        </div>
    </div>

    <script>
        const fileInput = document.getElementById('fileInput');
        const uploadArea = document.getElementById('uploadArea');
        const fileList = document.getElementById('fileList');
        const submitBtn = document.getElementById('submitBtn');
        const progressBar = document.getElementById('progressBar');
        const progressFill = document.getElementById('progressFill');
        const statusMessage = document.getElementById('statusMessage');

        let selectedFiles = [];

        // Drag and drop functionality
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = Array.from(e.dataTransfer.files);
            addFiles(files);
        });

        fileInput.addEventListener('change', (e) => {
            const files = Array.from(e.target.files);
            addFiles(files);
        });

        function addFiles(files) {
            files.forEach(file => {
                if (file.type.startsWith('image/')) {
                    selectedFiles.push(file);
                }
            });
            updateFileList();
            updateSubmitButton();
        }

        function removeFile(index) {
            selectedFiles.splice(index, 1);
            updateFileList();
            updateSubmitButton();
        }

        function updateFileList() {
            fileList.innerHTML = '';
            selectedFiles.forEach((file, index) => {
                const fileItem = document.createElement('div');
                fileItem.className = 'file-item';

                const reader = new FileReader();
                reader.onload = function(e) {
                    fileItem.innerHTML = `
                        <img src="${e.target.result}" class="file-preview" alt="Preview">
                        <div class="file-info">
                            <div class="file-name">${file.name}</div>
                            <div class="file-size">${formatFileSize(file.size)}</div>
                        </div>
                        <button type="button" class="remove-btn" onclick="removeFile(${index})">Remove</button>
                    `;
                };
                reader.readAsDataURL(file);

                fileList.appendChild(fileItem);
            });
        }

        function updateSubmitButton() {
            const eventSelected = document.getElementById('event').value;
            submitBtn.disabled = !(selectedFiles.length > 0 && eventSelected);
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        document.getElementById('event').addEventListener('change', updateSubmitButton);

        // Form submission
        document.getElementById('uploadForm').onsubmit = async function(e) {
            e.preventDefault();

            const formData = new FormData();
            formData.append('event', document.getElementById('event').value);

            selectedFiles.forEach(file => {
                formData.append('images', file);
            });

            // Show progress
            progressBar.style.display = 'block';
            submitBtn.disabled = true;
            statusMessage.style.display = 'none';

            try {
                const xhr = new XMLHttpRequest();

                xhr.upload.addEventListener('progress', (e) => {
                    if (e.lengthComputable) {
                        const percentComplete = (e.loaded / e.total) * 100;
                        progressFill.style.width = percentComplete + '%';
                    }
                });

                xhr.onload = function() {
                    progressBar.style.display = 'none';

                    if (xhr.status === 200) {
                        const response = JSON.parse(xhr.responseText);
                        if (response.success) {
                            statusMessage.className = 'status-message status-success';
                            statusMessage.textContent = response.message;
                            statusMessage.style.display = 'block';

                            // Reset form
                            selectedFiles = [];
                            updateFileList();
                            document.getElementById('event').value = '';
                            updateSubmitButton();
                        } else {
                            throw new Error(response.error || 'Upload failed');
                        }
                    } else {
                        throw new Error('Upload failed');
                    }
                };

                xhr.onerror = function() {
                    progressBar.style.display = 'none';
                    statusMessage.className = 'status-message status-error';
                    statusMessage.textContent = 'Upload failed. Please try again.';
                    statusMessage.style.display = 'block';
                    submitBtn.disabled = false;
                };

                xhr.open('POST', '');
                xhr.send(formData);

            } catch (error) {
                progressBar.style.display = 'none';
                statusMessage.className = 'status-message status-error';
                statusMessage.textContent = 'Upload failed: ' + error.message;
                statusMessage.style.display = 'block';
                submitBtn.disabled = false;
            }
        };
    </script>
</body>
</html>