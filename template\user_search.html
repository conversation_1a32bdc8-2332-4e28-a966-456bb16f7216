<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Find Your Photos - Face Recognition Photo Finder</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .upload-section {
            padding: 40px;
            text-align: center;
        }

        .upload-area {
            border: 3px dashed #667eea;
            border-radius: 15px;
            padding: 40px;
            margin: 20px 0;
            background: #f8f9ff;
            transition: all 0.3s ease;
        }

        .upload-area:hover {
            border-color: #764ba2;
            background: #f0f2ff;
        }

        .upload-area.dragover {
            border-color: #764ba2;
            background: #e8ebff;
            transform: scale(1.02);
        }

        .file-input {
            display: none;
        }

        .upload-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .upload-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .search-btn {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            color: white;
            border: none;
            padding: 15px 40px;
            border-radius: 25px;
            font-size: 1.2em;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 20px;
        }

        .search-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(17, 153, 142, 0.3);
        }

        .search-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .preview-image {
            max-width: 300px;
            max-height: 300px;
            border-radius: 10px;
            margin: 20px auto;
            display: block;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .results {
            padding: 40px;
            background: #f8f9fa;
        }

        .results h2 {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }

        .photo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }

        .photo-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .photo-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .photo-card img {
            width: 100%;
            height: 200px;
            object-fit: cover;
        }

        .photo-info {
            padding: 20px;
        }

        .photo-info h3 {
            margin-bottom: 10px;
            color: #333;
        }

        .confidence {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9em;
            display: inline-block;
            margin-bottom: 15px;
        }

        .download-btn {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
        }

        .download-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 107, 107, 0.3);
        }

        .error {
            background: #ff6b6b;
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: center;
        }

        .no-results {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .camera-section {
            margin: 20px 0;
            text-align: center;
        }

        #video {
            max-width: 100%;
            border-radius: 10px;
            margin: 20px 0;
        }

        .camera-btn {
            background: linear-gradient(135deg, #fd79a8 0%, #fdcb6e 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .camera-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(253, 121, 168, 0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Find Your Photos</h1>
            <p>Upload a selfie or take a photo to find all pictures where you appear</p>
        </div>

        <div class="upload-section">
            <form id="searchForm" enctype="multipart/form-data" method="post">
                {% csrf_token %}

                <div class="upload-area" id="uploadArea">
                    <h3>📸 Upload Your Photo</h3>
                    <p>Drag and drop your selfie here or click to browse</p>
                    <input type="file" name="selfie" accept="image/*" class="file-input" id="fileInput" required>
                    <button type="button" class="upload-btn" onclick="document.getElementById('fileInput').click()">
                        Choose Photo
                    </button>
                </div>

                <div class="camera-section">
                    <p>Or take a photo with your camera:</p>
                    <button type="button" class="camera-btn" id="cameraBtn">📷 Use Camera</button>
                    <video id="video" width="320" height="240" style="display: none;"></video>
                    <canvas id="canvas" style="display: none;"></canvas>
                    <button type="button" class="camera-btn" id="captureBtn" style="display: none;">📸 Capture Photo</button>
                </div>

                <img id="preview" class="preview-image" style="display: none;">

                <button type="submit" class="search-btn" id="searchBtn" disabled>
                    🔍 Find My Photos
                </button>
            </form>

            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>Searching for your photos...</p>
            </div>
        </div>

        <div class="results" id="results" style="display: none;">
            <h2>Your Photos</h2>
            <div id="photoGrid" class="photo-grid"></div>
        </div>
    </div>

    <script>
        let stream = null;

        // File upload handling
        const fileInput = document.getElementById('fileInput');
        const uploadArea = document.getElementById('uploadArea');
        const preview = document.getElementById('preview');
        const searchBtn = document.getElementById('searchBtn');

        // Drag and drop functionality
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                fileInput.files = files;
                handleFileSelect();
            }
        });

        fileInput.addEventListener('change', handleFileSelect);

        function handleFileSelect() {
            const file = fileInput.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    preview.src = e.target.result;
                    preview.style.display = 'block';
                    searchBtn.disabled = false;
                };
                reader.readAsDataURL(file);
            }
        }

        // Camera functionality
        const cameraBtn = document.getElementById('cameraBtn');
        const video = document.getElementById('video');
        const canvas = document.getElementById('canvas');
        const captureBtn = document.getElementById('captureBtn');

        cameraBtn.addEventListener('click', async () => {
            try {
                stream = await navigator.mediaDevices.getUserMedia({ video: true });
                video.srcObject = stream;
                video.style.display = 'block';
                captureBtn.style.display = 'inline-block';
                cameraBtn.textContent = '🛑 Stop Camera';
                cameraBtn.onclick = stopCamera;
                video.play();
            } catch (err) {
                alert('Camera access denied or not available');
            }
        });

        captureBtn.addEventListener('click', () => {
            const context = canvas.getContext('2d');
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;
            context.drawImage(video, 0, 0);

            canvas.toBlob((blob) => {
                const file = new File([blob], 'camera-photo.jpg', { type: 'image/jpeg' });
                const dataTransfer = new DataTransfer();
                dataTransfer.items.add(file);
                fileInput.files = dataTransfer.files;

                preview.src = canvas.toDataURL();
                preview.style.display = 'block';
                searchBtn.disabled = false;

                stopCamera();
            });
        });

        function stopCamera() {
            if (stream) {
                stream.getTracks().forEach(track => track.stop());
                video.style.display = 'none';
                captureBtn.style.display = 'none';
                cameraBtn.textContent = '📷 Use Camera';
                cameraBtn.onclick = () => cameraBtn.click();
            }
        }

        // Form submission
        document.getElementById('searchForm').onsubmit = async function(e) {
            e.preventDefault();

            const loading = document.getElementById('loading');
            const results = document.getElementById('results');
            const photoGrid = document.getElementById('photoGrid');

            loading.style.display = 'block';
            results.style.display = 'none';

            try {
                let formData = new FormData(this);
                let response = await fetch('', {
                    method: 'POST',
                    body: formData
                });

                let data = await response.json();
                loading.style.display = 'none';

                if (data.matches && data.matches.length > 0) {
                    results.style.display = 'block';
                    photoGrid.innerHTML = '';

                    data.matches.forEach(match => {
                        const photoCard = document.createElement('div');
                        photoCard.className = 'photo-card';
                        photoCard.innerHTML = `
                            <img src="${match.photo_url}" alt="Photo from ${match.event_name}">
                            <div class="photo-info">
                                <h3>${match.event_name}</h3>
                                <p>📅 ${match.event_date}</p>
                                <div class="confidence">✨ ${match.confidence}% match</div>
                                <button class="download-btn" onclick="downloadPhoto(${match.photo_id}, '${match.event_name}')">
                                    💾 Download Photo
                                </button>
                            </div>
                        `;
                        photoGrid.appendChild(photoCard);
                    });
                } else if (data.error) {
                    results.style.display = 'block';
                    photoGrid.innerHTML = `<div class="error">${data.error}</div>`;
                } else {
                    results.style.display = 'block';
                    photoGrid.innerHTML = `
                        <div class="no-results">
                            <h3>😔 No photos found</h3>
                            <p>We couldn't find any photos with your face. Try uploading a clearer photo or check back later for new events.</p>
                        </div>
                    `;
                }
            } catch (error) {
                loading.style.display = 'none';
                results.style.display = 'block';
                photoGrid.innerHTML = `<div class="error">An error occurred while searching. Please try again.</div>`;
            }
        };

        // Download functionality
        async function downloadPhoto(photoId, eventName) {
            try {
                const formData = new FormData();
                formData.append('photo_id', photoId);

                const response = await fetch('/download-photo/', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                if (data.success) {
                    // Create a temporary link to download the photo
                    const link = document.createElement('a');
                    link.href = data.download_url;
                    link.download = `${eventName}_photo_${photoId}.jpg`;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                } else {
                    alert('Download failed: ' + data.error);
                }
            } catch (error) {
                alert('Download failed. Please try again.');
            }
        }
    </script>
</body>
</html>