<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FaceScan Pro - AI Photo Recognition</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            --dark-gradient: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            --glass-bg: rgba(255, 255, 255, 0.1);
            --glass-border: rgba(255, 255, 255, 0.2);
            --text-primary: #2c3e50;
            --text-secondary: #7f8c8d;
            --shadow-light: 0 8px 32px rgba(31, 38, 135, 0.37);
            --shadow-heavy: 0 20px 60px rgba(0, 0, 0, 0.15);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: var(--primary-gradient);
            min-height: 100vh;
            overflow-x: hidden;
            position: relative;
        }

        /* Animated Background */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
            z-index: -1;
            animation: backgroundShift 20s ease-in-out infinite;
        }

        @keyframes backgroundShift {
            0%, 100% { transform: scale(1) rotate(0deg); }
            50% { transform: scale(1.1) rotate(5deg); }
        }

        /* Navigation */
        .navbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--glass-border);
            padding: 1rem 2rem;
            z-index: 1000;
            transition: all 0.3s ease;
        }

        .nav-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 12px;
            color: white;
            font-weight: 800;
            font-size: 1.5rem;
            text-decoration: none;
        }

        .logo-icon {
            width: 40px;
            height: 40px;
            background: var(--secondary-gradient);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            box-shadow: var(--shadow-light);
        }

        .nav-links {
            display: flex;
            gap: 2rem;
            align-items: center;
        }

        .nav-link {
            color: white;
            text-decoration: none;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .nav-link:hover {
            background: var(--glass-bg);
            transform: translateY(-2px);
        }

        .nav-link.active {
            background: var(--glass-bg);
            border: 1px solid var(--glass-border);
        }

        /* Main Container */
        .container {
            max-width: 1200px;
            margin: 100px auto 0;
            padding: 0 20px;
        }

        .hero-section {
            text-align: center;
            padding: 4rem 0;
            color: white;
        }

        .hero-title {
            font-size: clamp(2.5rem, 5vw, 4rem);
            font-weight: 800;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, #fff 0%, #f0f0f0 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            line-height: 1.2;
        }

        .hero-subtitle {
            font-size: 1.25rem;
            font-weight: 400;
            opacity: 0.9;
            margin-bottom: 3rem;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .main-card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 24px;
            padding: 3rem;
            box-shadow: var(--shadow-heavy);
            margin-bottom: 2rem;
        }

        /* Upload Section */
        .upload-section {
            text-align: center;
            margin-bottom: 3rem;
        }

        .upload-area {
            border: 2px dashed var(--glass-border);
            border-radius: 20px;
            padding: 3rem;
            background: rgba(255, 255, 255, 0.05);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .upload-area::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            transition: left 0.5s;
        }

        .upload-area:hover::before {
            left: 100%;
        }

        .upload-area:hover {
            border-color: rgba(255, 255, 255, 0.4);
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-5px);
            box-shadow: var(--shadow-heavy);
        }

        .upload-area.dragover {
            border-color: #4facfe;
            background: rgba(79, 172, 254, 0.1);
            transform: scale(1.02);
        }

        .upload-icon {
            font-size: 4rem;
            margin-bottom: 1.5rem;
            background: var(--success-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .upload-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: white;
            margin-bottom: 0.5rem;
        }

        .upload-subtitle {
            color: rgba(255, 255, 255, 0.8);
            font-size: 1rem;
            margin-bottom: 2rem;
        }

        .file-input {
            display: none;
        }

        /* Buttons */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 1rem 2rem;
            border: none;
            border-radius: 12px;
            font-weight: 600;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            text-decoration: none;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: var(--secondary-gradient);
            color: white;
            box-shadow: var(--shadow-light);
        }

        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(240, 147, 251, 0.4);
        }

        .btn-success {
            background: var(--success-gradient);
            color: white;
            box-shadow: var(--shadow-light);
        }

        .btn-success:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(79, 172, 254, 0.4);
        }

        .btn-warning {
            background: var(--warning-gradient);
            color: white;
            box-shadow: var(--shadow-light);
        }

        .btn-warning:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(67, 233, 123, 0.4);
        }

        .btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
            opacity: 0.6;
        }

        .btn:disabled::before {
            display: none;
        }

        .btn-large {
            padding: 1.25rem 3rem;
            font-size: 1.1rem;
            border-radius: 16px;
        }

        /* Loading Animation */
        .spinner {
            width: 50px;
            height: 50px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid #4facfe;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Photo Grid */
        .photo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }

        .photo-card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 20px;
            overflow: hidden;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
        }

        .photo-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(79, 172, 254, 0.1) 0%, rgba(240, 147, 251, 0.1) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .photo-card:hover::before {
            opacity: 1;
        }

        .photo-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
        }

        .photo-card img {
            width: 100%;
            height: 250px;
            object-fit: cover;
            transition: transform 0.4s ease;
        }

        .photo-card:hover img {
            transform: scale(1.05);
        }

        .photo-info {
            padding: 1.5rem;
            color: white;
        }

        .photo-info h3 {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .photo-info p {
            opacity: 0.8;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .confidence-badge {
            background: var(--success-gradient);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 1rem;
            box-shadow: var(--shadow-light);
        }

        .download-btn {
            width: 100%;
            background: var(--secondary-gradient);
            color: white;
            border: none;
            padding: 1rem;
            border-radius: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .download-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(240, 147, 251, 0.4);
        }

        /* Error and Success Messages */
        .alert {
            padding: 1.5rem;
            border-radius: 12px;
            margin: 1rem 0;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-weight: 500;
        }

        .alert-error {
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid rgba(239, 68, 68, 0.3);
            color: #ef4444;
        }

        .alert-success {
            background: rgba(34, 197, 94, 0.1);
            border: 1px solid rgba(34, 197, 94, 0.3);
            color: #22c55e;
        }

        .alert-info {
            background: rgba(59, 130, 246, 0.1);
            border: 1px solid rgba(59, 130, 246, 0.3);
            color: #3b82f6;
        }

        .no-results {
            text-align: center;
            padding: 3rem;
            color: rgba(255, 255, 255, 0.8);
        }

        .no-results-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.6;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .navbar {
                padding: 1rem;
            }

            .nav-content {
                flex-direction: column;
                gap: 1rem;
            }

            .nav-links {
                gap: 1rem;
            }

            .hero-title {
                font-size: 2.5rem;
            }

            .main-card {
                padding: 2rem;
                margin: 0 1rem;
            }

            .photo-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .btn {
                padding: 0.875rem 1.5rem;
                font-size: 0.9rem;
            }

            .btn-large {
                padding: 1rem 2rem;
                font-size: 1rem;
            }
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .fade-in-up {
            animation: fadeInUp 0.6s ease-out;
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        .pulse {
            animation: pulse 2s infinite;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-content">
            <a href="/" class="logo">
                <div class="logo-icon">
                    <i class="fas fa-search"></i>
                </div>
                <span>FaceScan Pro</span>
            </a>
            <div class="nav-links">
                <a href="/" class="nav-link active">
                    <i class="fas fa-camera"></i> Scan Photos
                </a>
                <a href="/dashboard/" class="nav-link">
                    <i class="fas fa-chart-bar"></i> Dashboard
                </a>
                <a href="/upload/" class="nav-link">
                    <i class="fas fa-upload"></i> Upload
                </a>
            </div>
        </div>
    </nav>

    <div class="container">
        <!-- Hero Section -->
        <div class="hero-section">
            <h1 class="hero-title">
                <i class="fas fa-brain" style="color: #4facfe;"></i>
                AI Photo Recognition
            </h1>
            <p class="hero-subtitle">
                Upload your photo and let our advanced AI find all pictures where you appear.
                Powered by cutting-edge face recognition technology.
            </p>
        </div>

        <!-- Main Upload Card -->
        <div class="main-card">
            <form id="searchForm" enctype="multipart/form-data" method="post">
                {% csrf_token %}

                <div class="upload-section">
                    <div class="upload-area" id="uploadArea">
                        <div class="upload-icon">
                            <i class="fas fa-cloud-upload-alt"></i>
                        </div>
                        <h3 class="upload-title">Upload Your Photo</h3>
                        <p class="upload-subtitle">Drag and drop your selfie here or click to browse</p>
                        <input type="file" name="selfie" accept="image/*" class="file-input" id="fileInput" required>
                        <button type="button" class="btn btn-primary" onclick="document.getElementById('fileInput').click()">
                            <i class="fas fa-image"></i>
                            Choose Photo
                        </button>
                    </div>
                </div>

                <div class="camera-section" style="margin-top: 2rem; padding-top: 2rem; border-top: 1px solid var(--glass-border);">
                    <h4 style="color: white; margin-bottom: 1rem; font-weight: 600;">
                        <i class="fas fa-video"></i> Or use your camera
                    </h4>
                    <div style="display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap;">
                        <button type="button" class="btn btn-warning" id="cameraBtn">
                            <i class="fas fa-camera"></i>
                            Start Camera
                        </button>
                        <button type="button" class="btn btn-success" id="captureBtn" style="display: none;">
                            <i class="fas fa-camera-retro"></i>
                            Capture Photo
                        </button>
                    </div>
                    <video id="video" width="320" height="240" style="display: none; margin-top: 1rem; border-radius: 12px; box-shadow: var(--shadow-light);"></video>
                    <canvas id="canvas" style="display: none;"></canvas>
                </div>

                <img id="preview" class="preview-image" style="display: none; max-width: 300px; border-radius: 16px; margin: 2rem auto; box-shadow: var(--shadow-heavy);">

                <div style="text-align: center; margin-top: 2rem;">
                    <button type="submit" class="btn btn-success btn-large" id="searchBtn" disabled>
                        <i class="fas fa-search"></i>
                        Find My Photos
                    </button>
                </div>
            </form>

            <div class="loading" id="loading" style="display: none; text-align: center; padding: 2rem;">
                <div class="spinner" style="margin: 0 auto 1rem;"></div>
                <p style="color: white; font-weight: 500;">
                    <i class="fas fa-brain"></i> AI is analyzing your photo...
                </p>
            </div>
        </div>

        <!-- Results Section -->
        <div class="results-section" id="results" style="display: none;">
            <div class="main-card">
                <h2 style="color: white; text-align: center; margin-bottom: 2rem; font-size: 2rem; font-weight: 700;">
                    <i class="fas fa-images" style="color: #4facfe;"></i>
                    Your Photos Found
                </h2>
                <div id="photoGrid" class="photo-grid"></div>
            </div>
        </div>
    </div>

    <script>
        let stream = null;

        // File upload handling
        const fileInput = document.getElementById('fileInput');
        const uploadArea = document.getElementById('uploadArea');
        const preview = document.getElementById('preview');
        const searchBtn = document.getElementById('searchBtn');

        // Drag and drop functionality
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                fileInput.files = files;
                handleFileSelect();
            }
        });

        fileInput.addEventListener('change', handleFileSelect);

        function handleFileSelect() {
            const file = fileInput.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    preview.src = e.target.result;
                    preview.style.display = 'block';
                    searchBtn.disabled = false;
                };
                reader.readAsDataURL(file);
            }
        }

        // Camera functionality
        const cameraBtn = document.getElementById('cameraBtn');
        const video = document.getElementById('video');
        const canvas = document.getElementById('canvas');
        const captureBtn = document.getElementById('captureBtn');

        cameraBtn.addEventListener('click', async () => {
            try {
                stream = await navigator.mediaDevices.getUserMedia({ video: true });
                video.srcObject = stream;
                video.style.display = 'block';
                captureBtn.style.display = 'inline-block';
                cameraBtn.textContent = '🛑 Stop Camera';
                cameraBtn.onclick = stopCamera;
                video.play();
            } catch (err) {
                alert('Camera access denied or not available');
            }
        });

        captureBtn.addEventListener('click', () => {
            const context = canvas.getContext('2d');
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;
            context.drawImage(video, 0, 0);

            canvas.toBlob((blob) => {
                const file = new File([blob], 'camera-photo.jpg', { type: 'image/jpeg' });
                const dataTransfer = new DataTransfer();
                dataTransfer.items.add(file);
                fileInput.files = dataTransfer.files;

                preview.src = canvas.toDataURL();
                preview.style.display = 'block';
                searchBtn.disabled = false;

                stopCamera();
            });
        });

        function stopCamera() {
            if (stream) {
                stream.getTracks().forEach(track => track.stop());
                video.style.display = 'none';
                captureBtn.style.display = 'none';
                cameraBtn.textContent = '📷 Use Camera';
                cameraBtn.onclick = () => cameraBtn.click();
            }
        }

        // Form submission
        document.getElementById('searchForm').onsubmit = async function(e) {
            e.preventDefault();

            const loading = document.getElementById('loading');
            const results = document.getElementById('results');
            const photoGrid = document.getElementById('photoGrid');

            loading.style.display = 'block';
            results.style.display = 'none';

            try {
                let formData = new FormData(this);
                let response = await fetch('', {
                    method: 'POST',
                    body: formData
                });

                let data = await response.json();
                loading.style.display = 'none';

                if (data.matches && data.matches.length > 0) {
                    results.style.display = 'block';
                    photoGrid.innerHTML = '';

                    data.matches.forEach((match, index) => {
                        const photoCard = document.createElement('div');
                        photoCard.className = 'photo-card fade-in-up';
                        photoCard.style.animationDelay = `${index * 0.1}s`;
                        photoCard.innerHTML = `
                            <img src="${match.photo_url}" alt="Photo from ${match.event_name}" loading="lazy">
                            <div class="photo-info">
                                <h3>
                                    <i class="fas fa-calendar-alt"></i>
                                    ${match.event_name}
                                </h3>
                                <p>
                                    <i class="fas fa-clock"></i>
                                    ${match.event_date}
                                </p>
                                <div class="confidence-badge">
                                    <i class="fas fa-brain"></i>
                                    ${match.confidence}% AI Match
                                </div>
                                <button class="download-btn" onclick="downloadPhoto(${match.photo_id}, '${match.event_name}')">
                                    <i class="fas fa-download"></i>
                                    Download Photo
                                </button>
                            </div>
                        `;
                        photoGrid.appendChild(photoCard);
                    });
                } else if (data.error) {
                    results.style.display = 'block';
                    photoGrid.innerHTML = `
                        <div class="alert alert-error">
                            <i class="fas fa-exclamation-triangle"></i>
                            ${data.error}
                        </div>
                    `;
                } else {
                    results.style.display = 'block';
                    photoGrid.innerHTML = `
                        <div class="no-results">
                            <div class="no-results-icon">
                                <i class="fas fa-search"></i>
                            </div>
                            <h3>No Photos Found</h3>
                            <p>We couldn't find any photos with your face. Try uploading a clearer photo or check back later for new events.</p>
                        </div>
                    `;
                }

                // Show demo note if available
                if (data.note) {
                    const noteDiv = document.createElement('div');
                    noteDiv.className = 'alert alert-info';
                    noteDiv.innerHTML = `
                        <i class="fas fa-info-circle"></i>
                        ${data.note}
                    `;
                    photoGrid.insertBefore(noteDiv, photoGrid.firstChild);
                }
            } catch (error) {
                loading.style.display = 'none';
                results.style.display = 'block';
                photoGrid.innerHTML = `
                    <div class="alert alert-error">
                        <i class="fas fa-exclamation-triangle"></i>
                        An error occurred while searching. Please try again.
                    </div>
                `;
            }
        };

        // Download functionality with better UX
        async function downloadPhoto(photoId, eventName) {
            const button = event.target;
            const originalText = button.innerHTML;

            try {
                // Show loading state
                button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Downloading...';
                button.disabled = true;

                const formData = new FormData();
                formData.append('photo_id', photoId);

                const response = await fetch('/download/', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                if (data.success) {
                    // Show success state
                    button.innerHTML = '<i class="fas fa-check"></i> Downloaded!';
                    button.style.background = 'var(--warning-gradient)';

                    // Create download link
                    const link = document.createElement('a');
                    link.href = data.download_url;
                    link.download = `${eventName}_photo_${photoId}.jpg`;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);

                    // Reset button after 2 seconds
                    setTimeout(() => {
                        button.innerHTML = originalText;
                        button.style.background = 'var(--secondary-gradient)';
                        button.disabled = false;
                    }, 2000);
                } else {
                    throw new Error(data.error || 'Download failed');
                }
            } catch (error) {
                // Show error state
                button.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Failed';
                button.style.background = '#ef4444';

                // Reset button after 2 seconds
                setTimeout(() => {
                    button.innerHTML = originalText;
                    button.style.background = 'var(--secondary-gradient)';
                    button.disabled = false;
                }, 2000);

                console.error('Download error:', error);
            }
        }

        // Add smooth scrolling to results
        function scrollToResults() {
            document.getElementById('results').scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            // Add some entrance animations
            document.querySelector('.hero-section').classList.add('fade-in-up');
            document.querySelector('.main-card').classList.add('fade-in-up');

            // Add navbar scroll effect
            window.addEventListener('scroll', function() {
                const navbar = document.querySelector('.navbar');
                if (window.scrollY > 50) {
                    navbar.style.background = 'rgba(255, 255, 255, 0.15)';
                } else {
                    navbar.style.background = 'var(--glass-bg)';
                }
            });
        });
    </script>
</body>
</html>