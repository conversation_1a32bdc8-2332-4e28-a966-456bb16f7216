<!DOCTYPE html>
<html>
<head>
    <title>Find Your Photos</title>
</head>
<body>
    <h1>Find Your Photos</h1>
    <form id="searchForm" enctype="multipart/form-data" method="post">
        {% csrf_token %}
        <input type="file" name="selfie" accept="image/*" required>
        <button type="submit">Search</button>
    </form>
    <div id="results"></div>
    <script>
        document.getElementById('searchForm').onsubmit = async function(e) {
            e.preventDefault();
            let formData = new FormData(this);
            let response = await fetch('', {
                method: 'POST',
                body: formData
            });
            let data = await response.json();
            let resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '';
            if (data.matches) {
                data.matches.forEach(url => {
                    let img = document.createElement('img');
                    img.src = url;
                    img.width = 200;
                    resultsDiv.appendChild(img);
                });
            } else {
                resultsDiv.innerText = data.error || 'No matches found.';
            }
        }
    </script>
</body>
</html> 