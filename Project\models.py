from django.db import models
from django.contrib.auth.models import User

# Create your models here.

class Event(models.Model):
    name = models.CharField(max_length=255)
    date = models.DateField()
    location = models.CharField(max_length=255)
    description = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True, null=True)
    is_active = models.BooleanField(default=True)

    class Meta:
        ordering = ['-date']

    def __str__(self):
        return f"{self.name} ({self.date})"

    @property
    def total_photos(self):
        return self.photos.count()

    @property
    def total_faces(self):
        return sum(photo.faces.count() for photo in self.photos.all())


class Photo(models.Model):
    event = models.ForeignKey(Event, on_delete=models.CASCADE, related_name='photos')
    image = models.ImageField(upload_to='event_photos/')
    uploaded_at = models.DateTimeField(auto_now_add=True)
    face_count = models.IntegerField(default=0)  # Cache face count for performance
    processed = models.BooleanField(default=False)  # Track if face detection is complete

    class Meta:
        ordering = ['-uploaded_at']

    def __str__(self):
        return f"Photo {self.id} for {self.event.name}"

    def update_face_count(self):
        """Update the cached face count"""
        self.face_count = self.faces.count()
        self.save(update_fields=['face_count'])


class FaceEncoding(models.Model):
    photo = models.ForeignKey(Photo, on_delete=models.CASCADE, related_name='faces')
    encoding = models.BinaryField()  # Store pickled numpy array
    bounding_box = models.CharField(max_length=255)  # JSON or stringified list
    confidence = models.FloatField(default=0.0)  # Face detection confidence
    created_at = models.DateTimeField(auto_now_add=True, null=True)

    def __str__(self):
        return f"Face in Photo {self.photo.id}"


class UserSearch(models.Model):
    """Track user searches for analytics"""
    search_time = models.DateTimeField(auto_now_add=True)
    matches_found = models.IntegerField(default=0)
    ip_address = models.GenericIPAddressField(null=True, blank=True)

    class Meta:
        ordering = ['-search_time']


class PhotoDownload(models.Model):
    """Track photo downloads/purchases"""
    photo = models.ForeignKey(Photo, on_delete=models.CASCADE, related_name='downloads')
    download_time = models.DateTimeField(auto_now_add=True)
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    email = models.EmailField(blank=True, null=True)  # Optional email for purchase tracking

    class Meta:
        ordering = ['-download_time']

    def __str__(self):
        return f"Download of Photo {self.photo.id} at {self.download_time}"
