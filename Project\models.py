from django.db import models

# Create your models here.

class Event(models.Model):
    name = models.CharField(max_length=255)
    date = models.DateField()
    location = models.CharField(max_length=255)

    def __str__(self):
        return f"{self.name} ({self.date})"

class Photo(models.Model):
    event = models.ForeignKey(Event, on_delete=models.CASCADE, related_name='photos')
    image = models.ImageField(upload_to='event_photos/')
    uploaded_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Photo {self.id} for {self.event.name}"

class FaceEncoding(models.Model):
    photo = models.ForeignKey(Photo, on_delete=models.CASCADE, related_name='faces')
    encoding = models.BinaryField()  # Store pickled numpy array
    bounding_box = models.CharField(max_length=255)  # JSON or stringified list

    def __str__(self):
        return f"Face in Photo {self.photo.id}"
