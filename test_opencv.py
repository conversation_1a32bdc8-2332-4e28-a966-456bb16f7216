#!/usr/bin/env python3
"""
Simple test script to check if OpenCV face detection is working
"""

def test_opencv_installation():
    """Test if OpenCV is properly installed"""
    try:
        import cv2
        print("✅ OpenCV imported successfully!")
        print(f"OpenCV version: {cv2.__version__}")
        
        # Test face cascade classifier
        face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
        if face_cascade.empty():
            print("❌ Face cascade classifier not found!")
            return False
        else:
            print("✅ Face cascade classifier loaded successfully!")
        
        return True
        
    except ImportError as e:
        print(f"❌ OpenCV not installed: {e}")
        return False
    except Exception as e:
        print(f"❌ Error testing OpenCV: {e}")
        return False

def test_numpy():
    """Test if NumPy is working"""
    try:
        import numpy as np
        print("✅ NumPy imported successfully!")
        print(f"NumPy version: {np.__version__}")
        return True
    except ImportError as e:
        print(f"❌ NumPy not installed: {e}")
        return False

def main():
    print("🔍 Testing Face Detection Dependencies...")
    print("=" * 50)
    
    numpy_ok = test_numpy()
    opencv_ok = test_opencv_installation()
    
    print("=" * 50)
    
    if numpy_ok and opencv_ok:
        print("🎉 All dependencies are working! Face detection should work.")
        print("\nNext steps:")
        print("1. Run: python manage.py migrate")
        print("2. Run: python manage.py createsuperuser")
        print("3. Run: python manage.py runserver")
        print("4. Visit: http://localhost:8000/api/find-your-photos/")
    else:
        print("❌ Some dependencies are missing. Please install:")
        if not numpy_ok:
            print("   pip install numpy")
        if not opencv_ok:
            print("   pip install opencv-python")

if __name__ == "__main__":
    main()
