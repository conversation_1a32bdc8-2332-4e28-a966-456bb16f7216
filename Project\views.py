from django.views import View
from django.http import JsonResponse
from django.shortcuts import render, redirect
from django.contrib.admin.views.decorators import staff_member_required
from django.utils.decorators import method_decorator
from django.core.files.storage import FileSystemStorage
from .models import Event, Photo, FaceEncoding
# import face_recognition
import numpy as np
import pickle
from django.views.decorators.csrf import csrf_exempt

@method_decorator(staff_member_required, name='dispatch')
class BulkUploadView(View):
    def get(self, request):
        events = Event.objects.all()
        return render(request, 'bulk_upload.html', {'events': events})

    def post(self, request):
        event_id = request.POST.get('event')
        event = Event.objects.get(id=event_id)
        files = request.FILES.getlist('images')
        for f in files:
            photo = Photo.objects.create(event=event, image=f)
            self.index_faces(photo)
        return redirect('/admin/')

    def index_faces(self, photo):
        image_path = photo.image.path
        # image = face_recognition.load_image_file(image_path)
        # face_locations = face_recognition.face_locations(image)
        # face_encodings = face_recognition.face_encodings(image, face_locations)
        # for location, encoding in zip(face_locations, face_encodings):
        #     FaceEncoding.objects.create(
        #         photo=photo,
        #         encoding=pickle.dumps(encoding),
        #         bounding_box=str(location)
        #     )

@method_decorator(csrf_exempt, name='dispatch')
class UserSearchView(View):
    def get(self, request):
        return render(request, 'user_search.html')

    # def post(self, request):
    #     uploaded_file = request.FILES['selfie']
    #     image = face_recognition.load_image_file(uploaded_file)
    #     encodings = face_recognition.face_encodings(image)
    #     if not encodings:
    #         return JsonResponse({'error': 'No face detected'}, status=400)
    #     user_encoding = encodings[0]
    #     matches = []
    #     for face in FaceEncoding.objects.all():
    #         known_encoding = pickle.loads(face.encoding)
    #         match = face_recognition.compare_faces([known_encoding], user_encoding, tolerance=0.5)
    #         if match[0]:
    #             matches.append(face.photo.image.url)
    #     return JsonResponse({'matches': matches})

