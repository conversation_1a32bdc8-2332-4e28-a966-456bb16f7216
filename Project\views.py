from django.views import View
from django.http import JsonResponse
from django.shortcuts import render, redirect
from django.contrib.admin.views.decorators import staff_member_required
from django.utils.decorators import method_decorator
from django.core.files.storage import FileSystemStorage
from django.conf import settings
from .models import Event, Photo, FaceEncoding, UserSearch, PhotoDownload
import os
import json
import pickle
import logging
from django.views.decorators.csrf import csrf_exempt

# Configure logging
logger = logging.getLogger(__name__)

# Try to import face recognition libraries
try:
    import face_recognition
    import numpy as np
    import cv2
    FACE_RECOGNITION_AVAILABLE = True
except ImportError as e:
    logger.warning(f"Face recognition libraries not available: {e}")
    FACE_RECOGNITION_AVAILABLE = False


@method_decorator(staff_member_required, name='dispatch')
class BulkUploadView(View):
    def get(self, request):
        events = Event.objects.all()
        return render(request, 'bulk_upload.html', {'events': events})

    def post(self, request):
        try:
            event_id = request.POST.get('event')
            if not event_id:
                return JsonResponse({'error': 'Event is required'}, status=400)

            event = Event.objects.get(id=event_id)
            files = request.FILES.getlist('images')

            if not files:
                return JsonResponse({'error': 'No images provided'}, status=400)

            uploaded_photos = []
            skipped_files = []

            for f in files:
                # Validate file
                validation_result = self.validate_image_file(f)
                if not validation_result['valid']:
                    skipped_files.append({
                        'name': f.name,
                        'reason': validation_result['reason']
                    })
                    continue

                try:
                    photo = Photo.objects.create(event=event, image=f)
                    uploaded_photos.append(photo)

                    # Index faces in the photo
                    self.index_faces(photo)

                except Exception as e:
                    logger.error(f"Error creating photo for file {f.name}: {str(e)}")
                    skipped_files.append({
                        'name': f.name,
                        'reason': f'Upload error: {str(e)}'
                    })
                    continue

            response_data = {
                'success': True,
                'message': f'Successfully uploaded {len(uploaded_photos)} photos',
                'photos_uploaded': len(uploaded_photos),
                'total_files': len(files)
            }

            if skipped_files:
                response_data['skipped_files'] = skipped_files
                response_data['message'] += f', {len(skipped_files)} files skipped'

            return JsonResponse(response_data)

        except Event.DoesNotExist:
            return JsonResponse({'error': 'Event not found'}, status=404)
        except Exception as e:
            logger.error(f"Error in bulk upload: {str(e)}")
            return JsonResponse({'error': 'Upload failed'}, status=500)

    def validate_image_file(self, file):
        """Validate uploaded image file"""
        # Check file type
        if not file.content_type.startswith('image/'):
            return {'valid': False, 'reason': 'Not an image file'}

        # Check file size (max 10MB)
        max_size = 10 * 1024 * 1024  # 10MB
        if file.size > max_size:
            return {'valid': False, 'reason': 'File too large (max 10MB)'}

        # Check file extension
        allowed_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']
        file_extension = os.path.splitext(file.name)[1].lower()
        if file_extension not in allowed_extensions:
            return {'valid': False, 'reason': 'Unsupported file format'}

        # Basic file validation
        if file.size == 0:
            return {'valid': False, 'reason': 'Empty file'}

        return {'valid': True, 'reason': None}

    def index_faces(self, photo):
        """Extract and store face encodings from uploaded photo"""
        if not FACE_RECOGNITION_AVAILABLE:
            logger.warning("Face recognition not available, skipping face indexing")
            photo.processed = True
            photo.save(update_fields=['processed'])
            return

        try:
            image_path = photo.image.path

            # Validate image file exists
            if not os.path.exists(image_path):
                logger.error(f"Image file not found: {image_path}")
                return

            # Load image with error handling
            try:
                image = face_recognition.load_image_file(image_path)
            except Exception as e:
                logger.error(f"Failed to load image {image_path}: {str(e)}")
                return

            # Find face locations and encodings
            face_locations = face_recognition.face_locations(image, model="hog")
            face_encodings = face_recognition.face_encodings(image, face_locations)

            # Store each face encoding
            face_count = 0
            for location, encoding in zip(face_locations, face_encodings):
                try:
                    # Calculate confidence based on face detection quality
                    confidence = self.calculate_face_confidence(location, image.shape)

                    FaceEncoding.objects.create(
                        photo=photo,
                        encoding=pickle.dumps(encoding),
                        bounding_box=json.dumps(location),
                        confidence=confidence
                    )
                    face_count += 1
                except Exception as e:
                    logger.error(f"Error storing face encoding: {str(e)}")
                    continue

            # Update photo with face count and processed status
            photo.face_count = face_count
            photo.processed = True
            photo.save(update_fields=['face_count', 'processed'])

            logger.info(f"Indexed {face_count} faces in photo {photo.id}")

        except Exception as e:
            logger.error(f"Error indexing faces for photo {photo.id}: {str(e)}")
            photo.processed = True  # Mark as processed even if failed to avoid reprocessing
            photo.save(update_fields=['processed'])

    def calculate_face_confidence(self, location, image_shape):
        """Calculate face detection confidence based on face size and position"""
        try:
            top, right, bottom, left = location
            face_width = right - left
            face_height = bottom - top
            image_height, image_width = image_shape[:2]

            # Calculate face area relative to image
            face_area = face_width * face_height
            image_area = image_width * image_height
            area_ratio = face_area / image_area

            # Base confidence on face size (larger faces are generally better)
            confidence = min(area_ratio * 10, 1.0)  # Cap at 1.0

            # Bonus for faces not too close to edges
            edge_margin = 0.1
            if (left > image_width * edge_margin and
                right < image_width * (1 - edge_margin) and
                top > image_height * edge_margin and
                bottom < image_height * (1 - edge_margin)):
                confidence *= 1.1

            return min(confidence, 1.0)

        except Exception:
            return 0.5  # Default confidence


@method_decorator(csrf_exempt, name='dispatch')
class UserSearchView(View):
    def get(self, request):
        return render(request, 'user_search.html')

    def post(self, request):
        """Handle user selfie upload and find matching photos"""
        if not FACE_RECOGNITION_AVAILABLE:
            return JsonResponse({
                'error': 'Face recognition service is not available'
            }, status=503)

        try:
            uploaded_file = request.FILES.get('selfie')
            if not uploaded_file:
                return JsonResponse({'error': 'No selfie provided'}, status=400)

            # Validate file type
            if not uploaded_file.content_type.startswith('image/'):
                return JsonResponse({'error': 'Invalid file type'}, status=400)

            # Load and process the uploaded image
            image = face_recognition.load_image_file(uploaded_file)
            encodings = face_recognition.face_encodings(image)

            if not encodings:
                return JsonResponse({'error': 'No face detected in the uploaded image'}, status=400)

            user_encoding = encodings[0]  # Use the first face found
            matches = []

            # Compare with all stored face encodings
            for face in FaceEncoding.objects.select_related('photo', 'photo__event'):
                try:
                    known_encoding = pickle.loads(face.encoding)

                    # Compare faces with tolerance
                    match = face_recognition.compare_faces(
                        [known_encoding],
                        user_encoding,
                        tolerance=0.6
                    )

                    if match[0]:
                        # Calculate face distance for confidence
                        distance = face_recognition.face_distance([known_encoding], user_encoding)[0]
                        confidence = 1 - distance  # Convert distance to confidence

                        matches.append({
                            'photo_url': request.build_absolute_uri(face.photo.image.url),
                            'photo_id': face.photo.id,
                            'event_name': face.photo.event.name,
                            'event_date': face.photo.event.date.strftime('%Y-%m-%d'),
                            'confidence': round(confidence * 100, 2)
                        })

                except Exception as e:
                    logger.error(f"Error processing face encoding {face.id}: {str(e)}")
                    continue

            # Sort matches by confidence (highest first)
            matches.sort(key=lambda x: x['confidence'], reverse=True)

            # Track user search for analytics
            UserSearch.objects.create(
                matches_found=len(matches),
                ip_address=self.get_client_ip(request)
            )

            return JsonResponse({
                'matches': matches,
                'total_matches': len(matches)
            })

        except Exception as e:
            logger.error(f"Error in user search: {str(e)}")
            return JsonResponse({'error': 'Search failed'}, status=500)

    def get_client_ip(self, request):
        """Get client IP address"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


@method_decorator(csrf_exempt, name='dispatch')
class PhotoDownloadView(View):
    """Handle photo downloads"""

    def post(self, request):
        try:
            photo_id = request.POST.get('photo_id')
            email = request.POST.get('email', '')  # Optional email

            if not photo_id:
                return JsonResponse({'error': 'Photo ID is required'}, status=400)

            try:
                photo = Photo.objects.get(id=photo_id)
            except Photo.DoesNotExist:
                return JsonResponse({'error': 'Photo not found'}, status=404)

            # Track download
            PhotoDownload.objects.create(
                photo=photo,
                email=email if email else None,
                ip_address=self.get_client_ip(request)
            )

            # Return download URL
            download_url = request.build_absolute_uri(photo.image.url)

            return JsonResponse({
                'success': True,
                'download_url': download_url,
                'photo_id': photo.id,
                'event_name': photo.event.name
            })

        except Exception as e:
            logger.error(f"Error in photo download: {str(e)}")
            return JsonResponse({'error': 'Download failed'}, status=500)

    def get_client_ip(self, request):
        """Get client IP address"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip