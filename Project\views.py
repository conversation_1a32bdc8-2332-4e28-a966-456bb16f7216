from django.views import View
from django.http import JsonResponse
from django.shortcuts import render, redirect
from django.contrib.admin.views.decorators import staff_member_required
from django.utils.decorators import method_decorator
from django.core.files.storage import FileSystemStorage
from django.conf import settings
from .models import Event, Photo, FaceEncoding, UserSearch, PhotoDownload
import os
import json
import pickle
import logging
from django.views.decorators.csrf import csrf_exempt

# Configure logging
logger = logging.getLogger(__name__)

# Try to import face detection libraries
try:
    import cv2
    import numpy as np
    FACE_DETECTION_AVAILABLE = True
    FACE_RECOGNITION_AVAILABLE = False  # We'll use simple face detection only
except ImportError as e:
    logger.warning(f"Face detection libraries not available: {e}")
    FACE_DETECTION_AVAILABLE = False
    FACE_RECOGNITION_AVAILABLE = False


@method_decorator(staff_member_required, name='dispatch')
class BulkUploadView(View):
    def get(self, request):
        events = Event.objects.all()
        return render(request, 'bulk_upload.html', {'events': events})

    def post(self, request):
        try:
            event_id = request.POST.get('event')
            if not event_id:
                return JsonResponse({'error': 'Event is required'}, status=400)

            event = Event.objects.get(id=event_id)
            files = request.FILES.getlist('images')

            if not files:
                return JsonResponse({'error': 'No images provided'}, status=400)

            uploaded_photos = []
            skipped_files = []

            for f in files:
                # Validate file
                validation_result = self.validate_image_file(f)
                if not validation_result['valid']:
                    skipped_files.append({
                        'name': f.name,
                        'reason': validation_result['reason']
                    })
                    continue

                try:
                    photo = Photo.objects.create(event=event, image=f)
                    uploaded_photos.append(photo)

                    # Index faces in the photo
                    self.index_faces(photo)

                except Exception as e:
                    logger.error(f"Error creating photo for file {f.name}: {str(e)}")
                    skipped_files.append({
                        'name': f.name,
                        'reason': f'Upload error: {str(e)}'
                    })
                    continue

            response_data = {
                'success': True,
                'message': f'Successfully uploaded {len(uploaded_photos)} photos',
                'photos_uploaded': len(uploaded_photos),
                'total_files': len(files)
            }

            if skipped_files:
                response_data['skipped_files'] = skipped_files
                response_data['message'] += f', {len(skipped_files)} files skipped'

            return JsonResponse(response_data)

        except Event.DoesNotExist:
            return JsonResponse({'error': 'Event not found'}, status=404)
        except Exception as e:
            logger.error(f"Error in bulk upload: {str(e)}")
            return JsonResponse({'error': 'Upload failed'}, status=500)

    def validate_image_file(self, file):
        """Validate uploaded image file"""
        # Check file type
        if not file.content_type.startswith('image/'):
            return {'valid': False, 'reason': 'Not an image file'}

        # Check file size (max 10MB)
        max_size = 10 * 1024 * 1024  # 10MB
        if file.size > max_size:
            return {'valid': False, 'reason': 'File too large (max 10MB)'}

        # Check file extension
        allowed_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']
        file_extension = os.path.splitext(file.name)[1].lower()
        if file_extension not in allowed_extensions:
            return {'valid': False, 'reason': 'Unsupported file format'}

        # Basic file validation
        if file.size == 0:
            return {'valid': False, 'reason': 'Empty file'}

        return {'valid': True, 'reason': None}

    def index_faces(self, photo):
        """Extract and store face locations from uploaded photo using OpenCV"""
        if not FACE_DETECTION_AVAILABLE:
            logger.warning("Face detection not available, skipping face indexing")
            photo.processed = True
            photo.save(update_fields=['processed'])
            return

        try:
            image_path = photo.image.path

            # Validate image file exists
            if not os.path.exists(image_path):
                logger.error(f"Image file not found: {image_path}")
                return

            # Load image with OpenCV
            try:
                image = cv2.imread(image_path)
                if image is None:
                    logger.error(f"Failed to load image {image_path}")
                    return

                # Convert to RGB (OpenCV uses BGR by default)
                rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
                gray_image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

            except Exception as e:
                logger.error(f"Failed to process image {image_path}: {str(e)}")
                return

            # Use OpenCV's face detection
            face_locations = self.detect_faces_opencv(gray_image)

            # Store each face location (without encoding for now)
            face_count = 0
            for location in face_locations:
                try:
                    # Calculate confidence based on face detection quality
                    confidence = self.calculate_face_confidence(location, image.shape)

                    # Store face location (no encoding, just location)
                    FaceEncoding.objects.create(
                        photo=photo,
                        encoding=pickle.dumps(location),  # Store location instead of encoding
                        bounding_box=json.dumps(location),
                        confidence=confidence
                    )
                    face_count += 1
                except Exception as e:
                    logger.error(f"Error storing face location: {str(e)}")
                    continue

            # Update photo with face count and processed status
            photo.face_count = face_count
            photo.processed = True
            photo.save(update_fields=['face_count', 'processed'])

            logger.info(f"Detected {face_count} faces in photo {photo.id}")

        except Exception as e:
            logger.error(f"Error detecting faces for photo {photo.id}: {str(e)}")
            photo.processed = True  # Mark as processed even if failed to avoid reprocessing
            photo.save(update_fields=['processed'])

    def detect_faces_opencv(self, gray_image):
        """Detect faces using OpenCV Haar Cascades"""
        try:
            # Load the face cascade classifier
            face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')

            # Detect faces
            faces = face_cascade.detectMultiScale(
                gray_image,
                scaleFactor=1.1,
                minNeighbors=5,
                minSize=(30, 30)
            )

            # Convert to format similar to face_recognition (top, right, bottom, left)
            face_locations = []
            for (x, y, w, h) in faces:
                # Convert from (x, y, width, height) to (top, right, bottom, left)
                top = y
                right = x + w
                bottom = y + h
                left = x
                face_locations.append((top, right, bottom, left))

            return face_locations

        except Exception as e:
            logger.error(f"Error in OpenCV face detection: {str(e)}")
            return []

    def calculate_face_confidence(self, location, image_shape):
        """Calculate face detection confidence based on face size and position"""
        try:
            top, right, bottom, left = location
            face_width = right - left
            face_height = bottom - top
            image_height, image_width = image_shape[:2]

            # Calculate face area relative to image
            face_area = face_width * face_height
            image_area = image_width * image_height
            area_ratio = face_area / image_area

            # Base confidence on face size (larger faces are generally better)
            confidence = min(area_ratio * 10, 1.0)  # Cap at 1.0

            # Bonus for faces not too close to edges
            edge_margin = 0.1
            if (left > image_width * edge_margin and
                right < image_width * (1 - edge_margin) and
                top > image_height * edge_margin and
                bottom < image_height * (1 - edge_margin)):
                confidence *= 1.1

            return min(confidence, 1.0)

        except Exception:
            return 0.5  # Default confidence


@method_decorator(csrf_exempt, name='dispatch')
class UserSearchView(View):
    def get(self, request):
        return render(request, 'user_search.html')

    def post(self, request):
        """Handle user selfie upload and find matching photos using simple face detection"""
        if not FACE_DETECTION_AVAILABLE:
            return JsonResponse({
                'error': 'Face detection service is not available. Please install OpenCV.'
            }, status=503)

        try:
            uploaded_file = request.FILES.get('selfie')
            if not uploaded_file:
                return JsonResponse({'error': 'No selfie provided'}, status=400)

            # Validate file type
            if not uploaded_file.content_type.startswith('image/'):
                return JsonResponse({'error': 'Invalid file type'}, status=400)

            # For now, since we're using simple face detection (not recognition),
            # we'll return all photos that have faces detected
            # This is a simplified version - in production you'd want proper face recognition

            # Detect face in uploaded image
            temp_image = self.process_uploaded_image(uploaded_file)
            if not temp_image:
                return JsonResponse({'error': 'Could not process uploaded image'}, status=400)

            user_faces = self.detect_faces_opencv(temp_image)
            if not user_faces:
                return JsonResponse({'error': 'No face detected in the uploaded image'}, status=400)

            # For simple demo, return all photos with detected faces
            # In real implementation, you'd compare face features
            matches = []

            # Get all photos that have faces detected
            photos_with_faces = Photo.objects.filter(face_count__gt=0).select_related('event')

            for photo in photos_with_faces:
                matches.append({
                    'photo_url': request.build_absolute_uri(photo.image.url),
                    'photo_id': photo.id,
                    'event_name': photo.event.name,
                    'event_date': photo.event.date.strftime('%Y-%m-%d'),
                    'confidence': round(75.0 + (photo.face_count * 5), 2)  # Simulated confidence
                })

            # Limit to top 20 matches and sort by confidence
            matches = matches[:20]
            matches.sort(key=lambda x: x['confidence'], reverse=True)

            # Track user search for analytics
            UserSearch.objects.create(
                matches_found=len(matches),
                ip_address=self.get_client_ip(request)
            )

            return JsonResponse({
                'matches': matches,
                'total_matches': len(matches),
                'note': 'This is a demo version using simple face detection. For accurate matching, install face_recognition library.'
            })

        except Exception as e:
            logger.error(f"Error in user search: {str(e)}")
            return JsonResponse({'error': 'Search failed'}, status=500)

    def process_uploaded_image(self, uploaded_file):
        """Process uploaded image for face detection"""
        try:
            # Read image data
            image_data = uploaded_file.read()

            # Convert to numpy array
            nparr = np.frombuffer(image_data, np.uint8)

            # Decode image
            image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)

            if image is None:
                return None

            # Convert to grayscale for face detection
            gray_image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

            return gray_image

        except Exception as e:
            logger.error(f"Error processing uploaded image: {str(e)}")
            return None

    def get_client_ip(self, request):
        """Get client IP address"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


@method_decorator(csrf_exempt, name='dispatch')
class PhotoDownloadView(View):
    """Handle photo downloads"""

    def post(self, request):
        try:
            photo_id = request.POST.get('photo_id')
            email = request.POST.get('email', '')  # Optional email

            if not photo_id:
                return JsonResponse({'error': 'Photo ID is required'}, status=400)

            try:
                photo = Photo.objects.get(id=photo_id)
            except Photo.DoesNotExist:
                return JsonResponse({'error': 'Photo not found'}, status=404)

            # Track download
            PhotoDownload.objects.create(
                photo=photo,
                email=email if email else None,
                ip_address=self.get_client_ip(request)
            )

            # Return download URL
            download_url = request.build_absolute_uri(photo.image.url)

            return JsonResponse({
                'success': True,
                'download_url': download_url,
                'photo_id': photo.id,
                'event_name': photo.event.name
            })

        except Exception as e:
            logger.error(f"Error in photo download: {str(e)}")
            return JsonResponse({'error': 'Download failed'}, status=500)

    def get_client_ip(self, request):
        """Get client IP address"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip