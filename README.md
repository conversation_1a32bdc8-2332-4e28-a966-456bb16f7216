# Face Recognition Photo Matching System

A Django-based web application that uses face recognition technology to help users find photos of themselves from events.

## Features

### Admin Panel (Studio Side)
- **Authentication**: Secure admin login system
- **Event Management**: Create and manage events with details (name, date, location)
- **Bulk Photo Upload**: Upload multiple photos at once with drag-and-drop interface
- **Automatic Face Detection**: AI-powered face detection and indexing
- **Analytics Dashboard**: View statistics on photos, faces, searches, and downloads
- **Enhanced Admin Interface**: Rich admin panel with photo previews and filters

### User Side (Public Facing)
- **Photo Upload**: Upload selfies via file browser or camera capture
- **Face Matching**: AI-powered face recognition to find matching photos
- **Photo Gallery**: View matched photos with confidence scores
- **Download System**: Download individual photos with tracking
- **Responsive Design**: Mobile-friendly interface

### Face Recognition System
- **Automatic Processing**: Face detection and encoding on photo upload
- **High Accuracy**: Uses state-of-the-art face recognition algorithms
- **Confidence Scoring**: Match confidence percentages for better results
- **Error Handling**: Robust error handling for various image formats

## Technology Stack

- **Backend**: Django 5.2.4 with Python
- **Face Recognition**: face_recognition library (dlib + OpenCV)
- **Database**: SQLite (easily upgradeable to PostgreSQL)
- **Frontend**: HTML5, CSS3, JavaScript (Vanilla)
- **File Storage**: Local filesystem (configurable for cloud storage)
- **Authentication**: Django's built-in authentication system

## Installation

### Prerequisites
- Python 3.8+
- pip
- Virtual environment (recommended)

### Setup Steps

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd face-recognition-system
   ```

2. **Create and activate virtual environment**
   ```bash
   python -m venv myvenv
   # On Windows:
   myvenv\Scripts\activate
   # On macOS/Linux:
   source myvenv/bin/activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Run migrations**
   ```bash
   python manage.py makemigrations
   python manage.py migrate
   ```

5. **Create superuser**
   ```bash
   python manage.py createsuperuser
   ```

6. **Run the development server**
   ```bash
   python manage.py runserver
   ```

## Usage

### Admin Panel
1. Access the admin panel at `http://localhost:8000/admin/`
2. Login with your superuser credentials
3. Create events in the Events section
4. Use bulk upload at `http://localhost:8000/admin/bulk-upload/` to upload photos
5. Photos are automatically processed for face detection

### User Interface
1. Visit `http://localhost:8000/find-your-photos/`
2. Upload a selfie or take a photo with your camera
3. The system will find and display matching photos
4. Download photos you want to keep

## API Endpoints

- `GET /find-your-photos/` - User photo search interface
- `POST /find-your-photos/` - Submit selfie for face matching
- `GET /admin/bulk-upload/` - Admin bulk upload interface
- `POST /admin/bulk-upload/` - Upload multiple photos
- `POST /download-photo/` - Download a specific photo

## Models

### Event
- `name`: Event name
- `date`: Event date
- `location`: Event location
- `description`: Optional description
- `is_active`: Active status

### Photo
- `event`: Foreign key to Event
- `image`: Image file
- `face_count`: Number of detected faces
- `processed`: Processing status

### FaceEncoding
- `photo`: Foreign key to Photo
- `encoding`: Pickled face encoding data
- `bounding_box`: Face location coordinates
- `confidence`: Detection confidence score

### UserSearch
- `search_time`: When the search occurred
- `matches_found`: Number of matches
- `ip_address`: User's IP address

### PhotoDownload
- `photo`: Downloaded photo
- `download_time`: When downloaded
- `email`: Optional user email
- `ip_address`: User's IP address

## Configuration

### Settings
Key settings in `core/settings.py`:
- `MEDIA_ROOT`: Photo storage location
- `MEDIA_URL`: Photo URL prefix
- Database configuration
- Debug settings

### Face Recognition
The system automatically detects if face recognition libraries are available and gracefully handles their absence for development.

## Testing

Run the test suite:
```bash
python manage.py test
```

Tests cover:
- Model functionality
- View responses
- File validation
- Authentication requirements
- Face recognition workflow

## Deployment Considerations

### Production Setup
1. Set `DEBUG = False`
2. Configure proper database (PostgreSQL recommended)
3. Set up static file serving
4. Configure media file storage (AWS S3 recommended)
5. Set up proper logging
6. Use environment variables for sensitive settings

### Performance Optimization
- Use Redis for caching
- Implement background task processing for face detection
- Optimize database queries
- Use CDN for media files

## Security Features

- CSRF protection
- File type validation
- File size limits
- Admin authentication required
- IP address tracking
- Secure file uploads

## Troubleshooting

### Common Issues
1. **Face recognition not working**: Ensure dlib and face_recognition are properly installed
2. **File upload errors**: Check file permissions and MEDIA_ROOT settings
3. **Database errors**: Run migrations and check database configuration
4. **Static files not loading**: Configure static file serving properly

### Logs
Check Django logs for detailed error information. The system logs face recognition operations and errors.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions, please create an issue in the repository or contact the development team.
