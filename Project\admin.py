from django.contrib import admin
from django.utils.html import format_html
from .models import Event, Photo, FaceEncoding, UserSearch, PhotoDownload


@admin.register(Event)
class EventAdmin(admin.ModelAdmin):
    list_display = ['name', 'date', 'location', 'total_photos', 'total_faces', 'is_active', 'created_at']
    list_filter = ['is_active', 'date', 'created_at']
    search_fields = ['name', 'location']
    readonly_fields = ['created_at', 'total_photos', 'total_faces']
    ordering = ['-date']

    def total_photos(self, obj):
        return obj.total_photos
    total_photos.short_description = 'Photos'

    def total_faces(self, obj):
        return obj.total_faces
    total_faces.short_description = 'Faces'


@admin.register(Photo)
class PhotoAdmin(admin.ModelAdmin):
    list_display = ['id', 'event', 'image_thumbnail', 'face_count', 'processed', 'uploaded_at']
    list_filter = ['processed', 'event', 'uploaded_at']
    search_fields = ['event__name']
    readonly_fields = ['uploaded_at', 'face_count', 'image_preview']
    ordering = ['-uploaded_at']

    def image_thumbnail(self, obj):
        if obj.image:
            return format_html(
                '<img src="{}" width="50" height="50" style="object-fit: cover;" />',
                obj.image.url
            )
        return "No Image"
    image_thumbnail.short_description = 'Thumbnail'

    def image_preview(self, obj):
        if obj.image:
            return format_html(
                '<img src="{}" width="300" style="max-width: 300px;" />',
                obj.image.url
            )
        return "No Image"
    image_preview.short_description = 'Preview'


@admin.register(FaceEncoding)
class FaceEncodingAdmin(admin.ModelAdmin):
    list_display = ['id', 'photo', 'confidence', 'created_at']
    list_filter = ['created_at', 'photo__event']
    search_fields = ['photo__event__name']
    readonly_fields = ['created_at', 'encoding', 'bounding_box']
    ordering = ['-created_at']


@admin.register(UserSearch)
class UserSearchAdmin(admin.ModelAdmin):
    list_display = ['search_time', 'matches_found', 'ip_address']
    list_filter = ['search_time', 'matches_found']
    readonly_fields = ['search_time', 'matches_found', 'ip_address']
    ordering = ['-search_time']


@admin.register(PhotoDownload)
class PhotoDownloadAdmin(admin.ModelAdmin):
    list_display = ['photo', 'download_time', 'email', 'ip_address']
    list_filter = ['download_time', 'photo__event']
    search_fields = ['email', 'photo__event__name']
    readonly_fields = ['download_time', 'ip_address']
    ordering = ['-download_time']
