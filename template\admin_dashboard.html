<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - Face Recognition System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
            min-height: 100vh;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header h1 {
            font-size: 2em;
        }
        
        .nav-links a {
            color: white;
            text-decoration: none;
            margin-left: 20px;
            padding: 10px 20px;
            border-radius: 5px;
            transition: background 0.3s ease;
        }
        
        .nav-links a:hover {
            background: rgba(255,255,255,0.2);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-number {
            font-size: 3em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .stat-label {
            font-size: 1.2em;
            color: #666;
        }
        
        .section {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        
        .section h2 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.5em;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .table th,
        .table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #e1e5e9;
        }
        
        .table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
        }
        
        .table tr:hover {
            background: #f8f9fa;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            margin: 5px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        }
        
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .action-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .action-card:hover {
            transform: translateY(-5px);
        }
        
        .action-icon {
            font-size: 3em;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <h1>📊 Admin Dashboard</h1>
            <div class="nav-links">
                <a href="/admin/">Django Admin</a>
                <a href="/admin/bulk-upload/">Bulk Upload</a>
                <a href="/find-your-photos/">User Interface</a>
                <a href="/admin/logout/">Logout</a>
            </div>
        </div>
    </div>
    
    <div class="container">
        <!-- Statistics Cards -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">{{ stats.total_events }}</div>
                <div class="stat-label">Total Events</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ stats.total_photos }}</div>
                <div class="stat-label">Total Photos</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ stats.total_faces }}</div>
                <div class="stat-label">Faces Detected</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ stats.total_searches }}</div>
                <div class="stat-label">User Searches</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ stats.total_downloads }}</div>
                <div class="stat-label">Photo Downloads</div>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="quick-actions">
            <div class="action-card">
                <div class="action-icon">📅</div>
                <h3>Create Event</h3>
                <a href="/admin/Project/event/add/" class="btn">Add New Event</a>
            </div>
            <div class="action-card">
                <div class="action-icon">📸</div>
                <h3>Upload Photos</h3>
                <a href="/admin/bulk-upload/" class="btn btn-success">Bulk Upload</a>
            </div>
            <div class="action-card">
                <div class="action-icon">👥</div>
                <h3>Manage Users</h3>
                <a href="/admin/auth/user/" class="btn">User Management</a>
            </div>
            <div class="action-card">
                <div class="action-icon">📊</div>
                <h3>View Reports</h3>
                <a href="/admin/Project/" class="btn">View All Data</a>
            </div>
        </div>
        
        <!-- Recent Events -->
        <div class="section">
            <h2>📅 Recent Events</h2>
            <table class="table">
                <thead>
                    <tr>
                        <th>Event Name</th>
                        <th>Date</th>
                        <th>Location</th>
                        <th>Photos</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for event in stats.recent_events %}
                    <tr>
                        <td>{{ event.name }}</td>
                        <td>{{ event.date }}</td>
                        <td>{{ event.location }}</td>
                        <td>{{ event.total_photos }}</td>
                        <td>
                            <a href="/admin/Project/event/{{ event.id }}/change/" class="btn">Edit</a>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="5" style="text-align: center; color: #666;">No events yet</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Recent Searches -->
        <div class="section">
            <h2>🔍 Recent User Searches</h2>
            <table class="table">
                <thead>
                    <tr>
                        <th>Search Time</th>
                        <th>Matches Found</th>
                        <th>IP Address</th>
                    </tr>
                </thead>
                <tbody>
                    {% for search in stats.recent_searches %}
                    <tr>
                        <td>{{ search.search_time|date:"M d, Y H:i" }}</td>
                        <td>{{ search.matches_found }}</td>
                        <td>{{ search.ip_address|default:"Unknown" }}</td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="3" style="text-align: center; color: #666;">No searches yet</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Recent Downloads -->
        <div class="section">
            <h2>💾 Recent Photo Downloads</h2>
            <table class="table">
                <thead>
                    <tr>
                        <th>Download Time</th>
                        <th>Event</th>
                        <th>Email</th>
                        <th>IP Address</th>
                    </tr>
                </thead>
                <tbody>
                    {% for download in stats.recent_downloads %}
                    <tr>
                        <td>{{ download.download_time|date:"M d, Y H:i" }}</td>
                        <td>{{ download.photo.event.name }}</td>
                        <td>{{ download.email|default:"Not provided" }}</td>
                        <td>{{ download.ip_address|default:"Unknown" }}</td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="4" style="text-align: center; color: #666;">No downloads yet</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</body>
</html>
